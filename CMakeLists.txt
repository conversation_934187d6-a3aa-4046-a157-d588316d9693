﻿# CMakeList.txt: gerrit_client 的 CMake 项目，在此处包括源代码并定义
# 项目特定的逻辑。
#
cmake_minimum_required(VERSION 3.10)
set(CMAKE_WIN32_EXECUTABLE ON)

# 如果支持，请为 MSVC 编译器启用热重载。
if (POLICY CMP0141)
  cmake_policy(SET CMP0141 NEW)
  set(CMAKE_MSVC_DEBUG_INFORMATION_FORMAT "$<IF:$<AND:$<C_COMPILER_ID:MSVC>,$<CXX_COMPILER_ID:MSVC>>,$<$<CONFIG:Debug,RelWithDebInfo>:EditAndContinue>,$<$<CONFIG:Debug,RelWithDebInfo>:ProgramDatabase>>")
endif()

# 设置C++标准
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

set(CMAKE_EXPORT_COMPILE_COMMANDS ON)  # 确保生成 compile_commands.json


# 强制使用 UTF-8 编码
add_compile_options("$<$<CXX_COMPILER_ID:MSVC>:/utf-8>")
# 启用 Unicode 支持
add_compile_definitions(UNICODE _UNICODE)

project("gerrit_client" VERSION 1.0.0 LANGUAGES C CXX)

# 添加子目录（子库）
# add_subdirectory(easy_direct_composition)

# 查找依赖包
#find_package(CURL REQUIRED)

# 创建主可执行文件
add_executable(gerrit_client
    "gerrit_client.cpp"
    "gerrit_client.h"
    easy_direct_composition/src/obj_tree.c
    easy_direct_composition/src/obj_tree.h
    easy_direct_composition/src/obj_helper.cpp
    easy_direct_composition/src/obj_helper.h
    easy_direct_composition/src/dc_env.cpp
    easy_direct_composition/src/dc_env.h
    easy_direct_composition/src/dc_surface.cpp
    easy_direct_composition/src/dc_surface.h

)

# 链接子库
#target_link_libraries(gerrit_client
#    PRIVATE
#        EasyDirectComposition::easy_direct_composition
#        #CURL::libcurl
#)
target_link_libraries(gerrit_client
    PUBLIC
        d3d11
        dcomp
        d2d1
        dwrite
        dxgi
        ole32
        uuid
        version
)


# 设置包含目录
target_include_directories(gerrit_client
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
)

# TODO: 如有需要，请添加测试并安装目标。
