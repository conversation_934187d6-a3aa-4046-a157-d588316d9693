#include <mutex>
#include <string>
#include <atlbase.h>
#include <dwrite.h>

#include "dc_surface.h"
#include "obj_helper.h"
DC_Surface_Helper::DC_Surface_Helper(Easy_Object surface_obj)
    : m_surface_obj(surface_obj) 
{
    assert(surface_obj.get("data").has_COM_interface(__uuidof(IDCompositionSurface)));
    if (surface_obj.get("components").is_null()) {
        surface_obj.insert("components", Easy_Object::make_map());
        Update_Closure_Data update_closure_data;
        update_closure_data.func = static_cast<void (*)(Update_Closure_Data*)>(compile);
        update_closure_data.surface_obj = surface_obj.get_ptr();
        surface_obj.insert("update", Easy_Object::make_raw(&update_closure_data, sizeof(Update_Closure_Data), alignof(Update_Closure_Data)));
        Easy_Object mutex_obj = Easy_Object::make_object<std::mutex>();
        surface_obj.insert("mutex", mutex_obj);
        m_mutex = (std::mutex*)mutex_obj.get_data_ptr();
    }else{
        m_mutex = (std::mutex*)surface_obj.get("mutex").get_data_ptr();
        assert(m_mutex);
    }
}

std::lock_guard<std::mutex> DC_Surface_Helper::lock()
{
    return std::lock_guard<std::mutex>(*m_mutex);
}

static void draw_rect(void* data, ID2D1DeviceContext* render_target, POINT offset)
{
    Rect_Data *rect_data = (Rect_Data*)data;
    ID2D1SolidColorBrush *brush;
    render_target->CreateSolidColorBrush(rect_data->color, &brush);
    render_target->FillRectangle(D2D1::RectF(rect_data->x, rect_data->y, rect_data->x + rect_data->width, rect_data->y + rect_data->height), brush);
}

Easy_Object DC_Surface_Helper::addRect(const std::string &name, Rect_Data rect_data)
{
    Easy_Object rect_obj = Easy_Object::make_map();
    m_surface_obj.get("components").insert(name, rect_obj);
    rect_obj.insert("type", Easy_Object::make_char32_string(U"rect"));
    Easy_Object rect_data_obj = Easy_Object::make_raw(&rect_data, sizeof(Rect_Data), alignof(Rect_Data));
    rect_obj.insert("data", rect_data_obj);
    Component_Draw_Function draw_func = draw_rect;
    rect_obj.insert("draw_func", Easy_Object::make_raw(&draw_func, sizeof(Component_Draw_Function*), alignof(Component_Draw_Function*)));
    return rect_obj;
}

static void draw_text(void* data, ID2D1DeviceContext* render_target, POINT offset)
{
    Text_Data *text_data = (Text_Data*)data;
    IDWriteTextFormat *text_format = text_data->text_format;
    ID2D1SolidColorBrush *brush;
    UINT32 textLen = (UINT32)wcslen(text_data->text);
    render_target->CreateSolidColorBrush(text_data->color, &brush);
    D2D1_RECT_F layoutRect = D2D1::RectF(text_data->x, text_data->y, text_data->x + 800,  text_data->y + 600);
    render_target->DrawText(
        text_data->text,
        textLen,
        text_format,
        layoutRect,
        brush
    );
}

Easy_Object DC_Surface_Helper::addText(const std::string &name, Text_Data text_data)
{
    Easy_Object text_obj = Easy_Object::make_map();
    m_surface_obj.get("components").insert(name, text_obj);
    text_obj.insert("type", Easy_Object::make_char32_string(U"text"));
    Easy_Object text_data_obj = Easy_Object::make_raw(&text_data, sizeof(Text_Data), alignof(Text_Data));
    text_obj.insert("data", text_data_obj);
    Component_Draw_Function draw_func = draw_text;
    text_obj.insert("draw_func", Easy_Object::make_raw(&draw_func, sizeof(Component_Draw_Function*), alignof(Component_Draw_Function*)));
    return text_obj;
}

static void draw_button(void* data, ID2D1DeviceContext* render_target, POINT offset)
{
    Button_Data *button_data = (Button_Data*)data;
    IDWriteTextFormat *text_format = button_data->text_format;
    ID2D1SolidColorBrush *brush;
    render_target->CreateSolidColorBrush(button_data->color, &brush);
    render_target->FillRectangle(D2D1::RectF(button_data->x, button_data->y, button_data->x + button_data->width, button_data->y + button_data->height), brush);
}

Easy_Object DC_Surface_Helper::addButton(const std::string &name, Button_Data button_data)
{
    Easy_Object button_obj = Easy_Object::make_map();
    m_surface_obj.get("components").insert(name, button_obj);
    button_obj.insert("type", Easy_Object::make_char32_string(U"button"));
    Easy_Object button_data_obj = Easy_Object::make_raw(&button_data, sizeof(Button_Data), alignof(Button_Data));
    button_obj.insert("data", button_data_obj);
    Component_Draw_Function draw_func = draw_button;
    button_obj.insert("draw_func", Easy_Object::make_raw(&draw_func, sizeof(Component_Draw_Function*), alignof(Component_Draw_Function*)));
    return button_obj;
}

void DC_Surface_Helper::compile()
{
    compile_i(m_surface_obj);
}

void DC_Surface_Helper::compile(Update_Closure_Data* closure)
{
    DC_Surface_Helper::compile_i(closure->surface_obj);
}

void DC_Surface_Helper::compile_i(Easy_Object surface_obj)
{
    Easy_Object surface_data = surface_obj.get("data");
    assert(!surface_data.is_null());
    Easy_Object mutex_obj = surface_obj.get("mutex");
    assert(!mutex_obj.is_null());
    std::lock_guard<std::mutex> lock(*((std::mutex*)mutex_obj.get_data_ptr()));
    CComPtr<ID2D1DeviceContext> d2dContext;
    surface_obj.get("context").get_COM_interface(d2dContext);
    CComPtr<IDCompositionSurface> surface;
    surface_data.get_COM_interface(surface);

    CComPtr<IDXGISurface> dxgiSurface;
    POINT offset = {0, 0};
    HRESULT hr = surface->BeginDraw(NULL, IID_PPV_ARGS(&dxgiSurface), &offset);
    if (FAILED(hr)) return;

    // 1. 获取表面描述
    DXGI_SURFACE_DESC surfaceDesc;
    dxgiSurface->GetDesc(&surfaceDesc);

    // 2. 创建正确的位图属性
    float dpiX, dpiY;
    d2dContext->GetDpi(&dpiX, &dpiY);

    D2D1_BITMAP_PROPERTIES1 bitmapProperties = D2D1::BitmapProperties1(
        D2D1_BITMAP_OPTIONS_TARGET | D2D1_BITMAP_OPTIONS_CANNOT_DRAW,
        D2D1::PixelFormat(
            surfaceDesc.Format,  // 使用表面实际格式
            D2D1_ALPHA_MODE_PREMULTIPLIED),  // 表面格式是DXGI_FORMAT_B8G8R8A8_UNORM
        dpiX,
        dpiY
    );

    // 3. 创建位图
    CComPtr<ID2D1Bitmap1> d2dTargetBitmap;
    hr = d2dContext->CreateBitmapFromDxgiSurface(
        dxgiSurface, 
        &bitmapProperties, 
        &d2dTargetBitmap
    );
    if (FAILED(hr)) return;
    d2dContext->SetTarget(d2dTargetBitmap);

    d2dContext->BeginDraw();
    d2dContext->Clear(D2D1::ColorF(0, 0, 0, 0));
    Easy_Object components = surface_obj.get("components");
    Map_Data *map_data = components.get_map_data();
    for (auto &pair : *map_data) {
        Easy_Object component = pair.second;
        Component_Draw_Function draw_func = *((Component_Draw_Function*)component.get("draw_func").get_data_ptr());
        void *data = component.get("data").get_data_ptr();
        draw_func(data, d2dContext, offset);
    }
    d2dContext->EndDraw();
    surface->EndDraw();
}
