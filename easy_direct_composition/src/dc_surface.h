/**
 * @file dc_surface.h
 * @brief Direct Composition Surface Management
 * @version 1.0.0
 *
 * This module provides surface helper classes for rendering graphics components
 * on Direct Composition surfaces. It supports various drawing primitives and
 * custom drawing functions.
 *
 * Features:
 * - Rectangle rendering with customizable colors
 * - Custom drawing component support
 * - Component management (add, remove, clear)
 * - Surface compilation and rendering
 */

#pragma once

#include <mutex>

#include <dcomp.h>
#include <d2d1.h>
#include <atlbase.h>
#include <assert.h>
#include <string>
#include <atlcomcli.h>
#include "obj_helper.h"

typedef struct s_Rect_Data {
    float x, y, width, height;
    D2D1_COLOR_F color;
} Rect_Data;

typedef struct s_Text_Data {
    float x, y;
    const wchar_t* text;
    D2D1_COLOR_F color;
    IDWriteTextFormat* text_format;
} Text_Data;

typedef struct s_Button_Data {
    float x, y, width, height;
    char32_t* text;
    D2D1_COLOR_F color;
    IDWriteTextFormat* text_format;
} Button_Data;


typedef void (*Component_Draw_Function)(void* data, ID2D1DeviceContext* render_target, POINT offset);

class DC_Surface_Helper {
    typedef struct s_Update_Closure_Data {
        void (*func)(struct s_Update_Closure_Data* surface_obj);
        Object* surface_obj;
    } Update_Closure_Data;
    
public:
    explicit DC_Surface_Helper(Easy_Object surface_obj);
    ~DC_Surface_Helper() {}
    DC_Surface_Helper(const DC_Surface_Helper&) = delete;
    DC_Surface_Helper& operator=(const DC_Surface_Helper&) = delete;
    std::lock_guard<std::mutex> lock();

    Easy_Object addRect(const std::string &name, Rect_Data rect_data);
    Easy_Object addText(const std::string &name, Text_Data text_data);
    Easy_Object addButton(const std::string &name, Button_Data button_data);

    template<typename T> T* getComponentData(Easy_Object component) const {
        if (component.is_null()) return nullptr;
        return (T*)component.get("data").get_data_ptr();
    } 

    Easy_Object getSurfaceObject() const {return m_surface_obj;}

    void compile();
private:
    static void compile(Update_Closure_Data* closure);
    static void compile_i(Easy_Object surface_obj);

    Easy_Object m_surface_obj;
    std::mutex* m_mutex;
    friend class DC_Env;
};