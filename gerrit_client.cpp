// gerrit_client.cpp: 定义应用程序的入口点。
//

#include "gerrit_client.h"
#include "easy_direct_composition/easy_direct_composition.h"
#include <Windowsx.h>

using namespace std;

Easy_Object rect, text;
CComPtr<IDWriteTextFormat> text_format;
edc::SurfaceHelper* global_surface_helper;
edc::Environment* global_dc_env;

LRESULT mouse_move_handler(edc::Object data, HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
	{
		std::lock_guard<std::mutex> lock(global_surface_helper->lock());
		int x_pos = GET_X_LPARAM(lParam);
		int y_pos = GET_Y_LPARAM(lParam);
		auto rect_data = global_surface_helper->getComponentData<edc::RectData>(rect);
		rect_data->x = (float)x_pos;
		rect_data->y = (float)y_pos;
		auto text_data = global_surface_helper->getComponentData<Text_Data>(text);
		text_data->x = (float)x_pos;
		text_data->y = (float)y_pos;
	}
	global_dc_env->markSurfaceDirty(global_surface_helper->getSurfaceObject());
	global_surface_helper->compile();
	global_dc_env->commit();
    return 0;
}

// Provides the entry point to the application.
INT WINAPI wWinMain(_In_ HINSTANCE hInstance, _In_opt_ HINSTANCE, _In_ LPWSTR, _In_ INT)
{
	// Initialize the Easy Direct Composition library
	Easy_Object_Seat root_object = edc::initialize();
	int ret = 0;
	{
		// Create the Direct Composition environment
		edc::Environment dc_inst(hInstance, root_object);
		assert(SUCCEEDED(dc_inst.Initialize()));
		global_dc_env = &dc_inst;

		// Create a visual and surface
		edc::Object visual1 = dc_inst.makeVisual(dc_inst.getRootVisual());
		edc::Object surface1 = dc_inst.createSurfaceForVisual(visual1, {0, 0, 400, 400});

		// Create surface helper and add a rectangle
		edc::SurfaceHelper surface_helper(surface1);
		global_surface_helper = &surface_helper;
		edc::RectData rect_data = edc::make_rect(100, 100, 100, 100, 0.6f, 1.0f, 1.0f, 0.9f);
		CComPtr<IDWriteFactory> dwriteFactory = dc_inst.getDwriteFactory();
		dwriteFactory->CreateTextFormat(
			L"Segoe UI",         // 字体
			nullptr,             // 字体集合(nullptr表示系统默认)
			DWRITE_FONT_WEIGHT_NORMAL,
			DWRITE_FONT_STYLE_NORMAL,
			DWRITE_FONT_STRETCH_NORMAL,
			24.0f,              // 字号
			L"en-us",           // 区域设置
			&text_format
		);
		text_format->SetTextAlignment(DWRITE_TEXT_ALIGNMENT_CENTER);
		text_format->SetParagraphAlignment(DWRITE_PARAGRAPH_ALIGNMENT_CENTER);
		Text_Data text_data = { 100,100,L"hello",{1.0f,0.0f,0.0f,0.5f},text_format };
		rect = surface_helper.addRect("rect1", rect_data);
		text = surface_helper.addText("text1", text_data);

		surface_helper.compile();

        dc_inst.registerEventHandler(WM_MOUSEMOVE, edc::Object::make_closure((void*)mouse_move_handler));

		// Commit changes and run the message loop
		dc_inst.commit();
		ret = dc_inst.Run();
	}
	edc::cleanup();
	return ret;
}

